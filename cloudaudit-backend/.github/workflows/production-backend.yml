name: Deploy Backend to Production EC2

on:
  push:
    branches:
      - main
      - master
    paths:
      - 'cloudaudit-backend/**'
  workflow_dispatch:
    inputs:
      confirm_production_deploy:
        description: 'Type "DEPLOY" to confirm production deployment'
        required: true
        default: ''

env:
  AWS_REGION: us-east-1
  EC2_HOST: ${{ secrets.PRODUCTION_EC2_HOST }}
  EC2_USER: ubuntu
  DEPLOY_PATH: /home/<USER>/cloudaudit/cloudaudit-backend

jobs:
  deploy:
    name: Deploy to Production EC2
    runs-on: ubuntu-latest
    environment: production
    
    steps:
    - name: Validate Production Deployment
      if: github.event_name == 'workflow_dispatch'
      run: |
        if [ "${{ github.event.inputs.confirm_production_deploy }}" != "DEPLOY" ]; then
          echo "❌ Production deployment not confirmed. Please type 'DEPLOY' to proceed."
          exit 1
        fi
        echo "✅ Production deployment confirmed"
        
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        cd cloudaudit-backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Run tests
      run: |
        cd cloudaudit-backend
        # Add your test commands here
        # python -m pytest tests/ --verbose
        echo "Production tests would run here - ensure all tests pass before production deployment"
        
    - name: Security scan (optional)
      run: |
        cd cloudaudit-backend
        # Add security scanning tools here
        # pip install safety bandit
        # safety check
        # bandit -r app/
        echo "Security scans would run here"
        
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
        
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.PRODUCTION_EC2_PRIVATE_KEY }}" > ~/.ssh/production_key
        chmod 600 ~/.ssh/production_key
        ssh-keyscan -H ${{ env.EC2_HOST }} >> ~/.ssh/known_hosts
        
    - name: Create Backup
      run: |
        # Create backup script
        cat > backup.sh << 'EOF'
        #!/bin/bash
        set -e
        
        echo "Creating backup before deployment..."
        
        # Create backup directory with timestamp
        BACKUP_DIR="/home/<USER>/backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p $BACKUP_DIR
        
        # Backup current application
        if [ -d "${{ env.DEPLOY_PATH }}" ]; then
          cp -r ${{ env.DEPLOY_PATH }} $BACKUP_DIR/
          echo "Backup created at: $BACKUP_DIR"
        fi
        
        # Keep only last 5 backups
        cd /home/<USER>/backups
        ls -t | tail -n +6 | xargs -r rm -rf
        
        echo "Backup completed successfully!"
        EOF
        
        chmod +x backup.sh
        scp -i ~/.ssh/production_key backup.sh ${{ env.EC2_USER }}@${{ env.EC2_HOST }}:/tmp/
        ssh -i ~/.ssh/production_key ${{ env.EC2_USER }}@${{ env.EC2_HOST }} "bash /tmp/backup.sh"
        
    - name: Deploy to Production EC2
      run: |
        # Create deployment script
        cat > deploy.sh << 'EOF'
        #!/bin/bash
        set -e
        
        echo "Starting deployment to production..."
        
        # Navigate to project directory
        cd ${{ env.DEPLOY_PATH }}
        
        # Pull latest changes
        git fetch origin
        git reset --hard origin/main
        
        # Activate virtual environment and install/update dependencies
        source /home/<USER>/cloudaudit/venv/bin/activate
        pip install -r requirements.txt
        
        # Stop services gracefully
        echo "Stopping services..."
        sudo systemctl stop cloudaudit-backend.service || true
        sudo systemctl stop celery-worker1.service || true
        sudo systemctl stop celery-worker2.service || true
        
        # Wait for services to stop completely
        sleep 10
        
        # Start services
        echo "Starting services..."
        sudo systemctl start cloudaudit-backend.service
        sudo systemctl start celery-worker1.service
        sudo systemctl start celery-worker2.service
        
        # Enable services to start on boot
        sudo systemctl enable cloudaudit-backend.service
        sudo systemctl enable celery-worker1.service
        sudo systemctl enable celery-worker2.service
        
        # Check service status
        echo "Checking service status..."
        sudo systemctl status cloudaudit-backend.service --no-pager
        sudo systemctl status celery-worker1.service --no-pager
        sudo systemctl status celery-worker2.service --no-pager
        
        echo "Production deployment completed successfully!"
        EOF
        
        # Make script executable and run it on EC2
        chmod +x deploy.sh
        scp -i ~/.ssh/production_key deploy.sh ${{ env.EC2_USER }}@${{ env.EC2_HOST }}:/tmp/
        ssh -i ~/.ssh/production_key ${{ env.EC2_USER }}@${{ env.EC2_HOST }} "bash /tmp/deploy.sh"
        
    - name: Health Check
      run: |
        # Wait for services to start
        sleep 45
        
        # Comprehensive health check
        ssh -i ~/.ssh/production_key ${{ env.EC2_USER }}@${{ env.EC2_HOST }} "
          echo 'Performing health checks...'
          
          # Check if services are running
          sudo systemctl is-active cloudaudit-backend.service
          sudo systemctl is-active celery-worker1.service
          sudo systemctl is-active celery-worker2.service
          
          # Check service logs for errors
          echo 'Checking recent logs...'
          sudo journalctl -u cloudaudit-backend.service --since '1 minute ago' --no-pager | tail -10
          
          # Optional: Check if the API is responding (adjust port if needed)
          # curl -f http://localhost:8000/health || echo 'Health check endpoint not available'
          
          echo 'Health checks completed!'
        "
        
    - name: Rollback on Failure
      if: failure()
      run: |
        echo "❌ Deployment failed! Attempting rollback..."
        # Add rollback logic here if needed
        # This could restore from the backup created earlier
        
    - name: Cleanup
      if: always()
      run: |
        rm -f ~/.ssh/production_key
        ssh -i ~/.ssh/production_key ${{ env.EC2_USER }}@${{ env.EC2_HOST }} "rm -f /tmp/deploy.sh /tmp/backup.sh" || true
        
    - name: Notify on Success
      if: success()
      run: |
        echo "✅ Production deployment successful!"
        # Add notification logic here (Slack, email, etc.)
        
    - name: Notify on Failure
      if: failure()
      run: |
        echo "❌ Production deployment failed!"
        # Add notification logic here (Slack, email, etc.)
