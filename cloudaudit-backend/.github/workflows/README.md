# CloudAudit Backend CI/CD Documentation

This directory contains GitHub Actions workflows for automated deployment of the CloudAudit backend to EC2 instances.

## Workflows

### 1. Staging Deployment (`staging-backend.yml`)
- **Trigger**: Push to `staging` branch or manual dispatch
- **Target**: Staging EC2 instance
- **Purpose**: Automated deployment for testing and validation

### 2. Production Deployment (`production-backend.yml`)
- **Trigger**: Push to `main`/`master` branch or manual dispatch
- **Target**: Production EC2 instance
- **Purpose**: Controlled deployment to production environment
- **Safety**: Requires manual confirmation for workflow_dispatch

## Required GitHub Secrets

Configure the following secrets in your GitHub repository settings:

### AWS Credentials
```
AWS_ACCESS_KEY_ID          # AWS access key for EC2 access
AWS_SECRET_ACCESS_KEY      # AWS secret key for EC2 access
```

### Staging Environment
```
STAGING_EC2_HOST           # IP address or hostname of staging EC2
STAGING_EC2_PRIVATE_KEY    # Private SSH key for staging EC2 access
```

### Production Environment
```
PRODUCTION_EC2_HOST        # IP address or hostname of production EC2
PRODUCTION_EC2_PRIVATE_KEY # Private SSH key for production EC2 access
```

## EC2 Setup Requirements

### Prerequisites on EC2 Instances

1. **Git Repository**: Ensure the repository is cloned at `/home/<USER>/cloudaudit/`
2. **Python Virtual Environment**: Create at `/home/<USER>/cloudaudit/venv/`
3. **Systemd Services**: Configure the following services:
   - `cloudaudit-backend.service`
   - `celery-worker1.service`
   - `celery-worker2.service`

### Systemd Service Files

Place these files in `/etc/systemd/system/`:

#### cloudaudit-backend.service
```ini
[Unit]
Description=CloudAudit Backend App
After=network.target

[Service]
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/cloudaudit/cloudaudit-backend
ExecStart=/home/<USER>/cloudaudit/venv/bin/python run.py
Restart=always

[Install]
WantedBy=multi-user.target
```

#### celery-worker1.service
```ini
[Unit]
Description=Celery Worker 1 for FastAPI
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/cloudaudit/cloudaudit-backend
Environment="PATH=/home/<USER>/cloudaudit/venv/bin"
ExecStart=/home/<USER>/cloudaudit/venv/bin/celery -A app.core.services.celery_conf.celery.celery_obj worker --loglevel=info -c 10 --hostname=worker1@%h
Restart=always
RestartSec=10s

[Install]
WantedBy=multi-user.target
```

#### celery-worker2.service
```ini
[Unit]
Description=Celery Worker 2 for FastAPI
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/cloudaudit/cloudaudit-backend
Environment="PATH=/home/<USER>/cloudaudit/venv/bin"
ExecStart=/home/<USER>/cloudaudit/venv/bin/celery -A app.core.services.celery_conf.celery.celery_obj worker --loglevel=info -c 10 --hostname=worker2@%h
Restart=always
RestartSec=10s

[Install]
WantedBy=multi-user.target
```

### SSH Key Setup

1. Generate SSH key pair for GitHub Actions
2. Add public key to EC2 instance: `~/.ssh/authorized_keys`
3. Add private key to GitHub secrets

### Permissions Setup

Ensure the `ubuntu` user has sudo permissions for systemctl commands:

```bash
# Add to /etc/sudoers.d/ubuntu
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl start cloudaudit-backend.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl stop cloudaudit-backend.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl restart cloudaudit-backend.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl enable cloudaudit-backend.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl status cloudaudit-backend.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl is-active cloudaudit-backend.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl start celery-worker*.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl stop celery-worker*.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl restart celery-worker*.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl enable celery-worker*.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl status celery-worker*.service
ubuntu ALL=(ALL) NOPASSWD: /bin/systemctl is-active celery-worker*.service
ubuntu ALL=(ALL) NOPASSWD: /bin/journalctl -u cloudaudit-backend.service *
ubuntu ALL=(ALL) NOPASSWD: /bin/journalctl -u celery-worker*.service *
```

## Deployment Process

### Staging Deployment
1. Push changes to `staging` branch
2. GitHub Actions automatically deploys to staging EC2
3. Services are restarted automatically
4. Health checks verify deployment

### Production Deployment
1. **Automatic**: Push to `main`/`master` branch
2. **Manual**: Use workflow_dispatch with confirmation
3. Backup is created before deployment
4. Services are gracefully restarted
5. Comprehensive health checks
6. Rollback capability on failure

## Monitoring and Troubleshooting

### Check Service Status
```bash
sudo systemctl status cloudaudit-backend.service
sudo systemctl status celery-worker1.service
sudo systemctl status celery-worker2.service
```

### View Logs
```bash
sudo journalctl -u cloudaudit-backend.service -f
sudo journalctl -u celery-worker1.service -f
sudo journalctl -u celery-worker2.service -f
```

### Manual Service Control
```bash
# Stop services
sudo systemctl stop cloudaudit-backend.service celery-worker1.service celery-worker2.service

# Start services
sudo systemctl start cloudaudit-backend.service celery-worker1.service celery-worker2.service

# Restart services
sudo systemctl restart cloudaudit-backend.service celery-worker1.service celery-worker2.service
```

## Security Considerations

1. **SSH Keys**: Use dedicated SSH keys for CI/CD
2. **Secrets Management**: Store all sensitive data in GitHub Secrets
3. **Network Security**: Ensure EC2 security groups allow SSH from GitHub Actions IPs
4. **User Permissions**: Limit sudo permissions to required systemctl commands only
5. **Backup**: Regular backups are created before production deployments

## Customization

### Adding Tests
Uncomment and modify the test sections in the workflows:
```yaml
- name: Run tests
  run: |
    cd cloudaudit-backend
    python -m pytest tests/ --verbose
```

### Adding Notifications
Add notification steps for Slack, email, or other services:
```yaml
- name: Notify on Success
  if: success()
  run: |
    # Add your notification logic here
    curl -X POST -H 'Content-type: application/json' \
      --data '{"text":"✅ Deployment successful!"}' \
      ${{ secrets.SLACK_WEBHOOK_URL }}
```

### Health Check Endpoints
If you have health check endpoints, uncomment and modify:
```yaml
curl -f http://localhost:8000/health
```
