name: Deploy Backend to Staging EC2

on:
  push:
    branches:
      - staging
    paths:
      - 'cloudaudit-backend/**'
  workflow_dispatch:

env:
  AWS_REGION: us-east-1
  EC2_HOST: ${{ secrets.STAGING_EC2_HOST }}
  EC2_USER: ubuntu
  DEPLOY_PATH: /home/<USER>/cloudaudit/cloudaudit-backend

jobs:
  deploy:
    name: Deploy to Staging EC2
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        cd cloudaudit-backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Run tests (if any)
      run: |
        cd cloudaudit-backend
        # Add your test commands here
        # python -m pytest tests/ || true
        echo "Tests would run here"
        
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
        
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.STAGING_EC2_PRIVATE_KEY }}" > ~/.ssh/staging_key
        chmod 600 ~/.ssh/staging_key
        ssh-keyscan -H ${{ env.EC2_HOST }} >> ~/.ssh/known_hosts
        
    - name: Deploy to EC2
      run: |
        # Create deployment script
        cat > deploy.sh << 'EOF'
        #!/bin/bash
        set -e
        
        echo "Starting deployment to staging..."
        
        # Navigate to project directory
        cd ${{ env.DEPLOY_PATH }}
        
        # Pull latest changes
        git fetch origin
        git reset --hard origin/staging
        
        # Activate virtual environment and install/update dependencies
        source /home/<USER>/cloudaudit/venv/bin/activate
        pip install -r requirements.txt
        
        # Stop services
        sudo systemctl stop cloudaudit-backend.service || true
        sudo systemctl stop celery-worker1.service || true
        sudo systemctl stop celery-worker2.service || true
        
        # Wait a moment for services to stop
        sleep 5
        
        # Start services
        sudo systemctl start cloudaudit-backend.service
        sudo systemctl start celery-worker1.service
        sudo systemctl start celery-worker2.service
        
        # Enable services to start on boot
        sudo systemctl enable cloudaudit-backend.service
        sudo systemctl enable celery-worker1.service
        sudo systemctl enable celery-worker2.service
        
        # Check service status
        sudo systemctl status cloudaudit-backend.service --no-pager
        sudo systemctl status celery-worker1.service --no-pager
        sudo systemctl status celery-worker2.service --no-pager
        
        echo "Deployment completed successfully!"
        EOF
        
        # Make script executable and run it on EC2
        chmod +x deploy.sh
        scp -i ~/.ssh/staging_key deploy.sh ${{ env.EC2_USER }}@${{ env.EC2_HOST }}:/tmp/
        ssh -i ~/.ssh/staging_key ${{ env.EC2_USER }}@${{ env.EC2_HOST }} "bash /tmp/deploy.sh"
        
    - name: Health Check
      run: |
        # Wait for services to start
        sleep 30
        
        # Check if the application is responding
        ssh -i ~/.ssh/staging_key ${{ env.EC2_USER }}@${{ env.EC2_HOST }} "
          # Check if services are running
          sudo systemctl is-active cloudaudit-backend.service
          sudo systemctl is-active celery-worker1.service
          sudo systemctl is-active celery-worker2.service
          
          # Optional: Check if the API is responding (adjust port if needed)
          # curl -f http://localhost:8000/health || echo 'Health check endpoint not available'
        "
        
    - name: Cleanup
      if: always()
      run: |
        rm -f ~/.ssh/staging_key
        ssh -i ~/.ssh/staging_key ${{ env.EC2_USER }}@${{ env.EC2_HOST }} "rm -f /tmp/deploy.sh" || true
        
    - name: Notify on Success
      if: success()
      run: |
        echo "✅ Staging deployment successful!"
        # Add notification logic here (Slack, email, etc.)
        
    - name: Notify on Failure
      if: failure()
      run: |
        echo "❌ Staging deployment failed!"
        # Add notification logic here (Slack, email, etc.)
